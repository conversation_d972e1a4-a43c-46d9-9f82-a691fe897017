<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备监控界面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            width: 1060px;
            height: 570px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 0;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            display: flex;
            overflow: hidden;
            backdrop-filter: blur(10px);
        }

        .left-region {
            width: 30%;
            background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
            padding: 20px;
            display: flex;
            flex-direction: column;
            gap: 20px;
            color: white;
        }

        .right-region {
            width: 70%;
            background: linear-gradient(180deg, #ecf0f1 0%, #bdc3c7 100%);
            padding: 25px;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .component {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .indicator {
            display: flex;
            align-items: center;
            gap: 20px;
            height: 100%;
        }

        .status-circle {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: #e74c3c;
            box-shadow: 0 0 10px rgba(231, 76, 60, 0.5);
            animation: pulse 2s infinite;
        }

        .status-circle.running {
            background: #27ae60;
            box-shadow: 0 0 10px rgba(39, 174, 96, 0.5);
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .text-component {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .title {
            font-size: 28px;
            font-weight: bold;
            color: #2c3e50;
            text-align: center;
            margin-bottom: 15px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }

        .parameter {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: transform 0.3s ease;
            flex: 1;
        }

        .parameter:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .parameter-label {
            font-size: 18px;
            font-weight: 600;
            color: #34495e;
        }

        .parameter-value {
            font-size: 20px;
            font-weight: bold;
            color: #2c3e50;
            background: linear-gradient(45deg, #3498db, #2980b9);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .unit {
            font-size: 14px;
            color: #7f8c8d;
            margin-left: 5px;
        }

        .label {
            font-size: 16px;
            font-weight: 500;
            color: rgba(255, 255, 255, 0.9);
        }

        .value {
            font-size: 18px;
            font-weight: bold;
            color: white;
        }

        .fan-direction {
            display: flex;
            gap: 15px;
            justify-content: center;
            align-items: center;
            height: 100%;
        }

        .direction-option {
            padding: 8px 15px;
            border-radius: 8px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .direction-option.active {
            background: #3498db;
            color: white;
        }

        .direction-option {
            background: rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.5);
        }

        .rpm-display {
            font-size: 28px;
            font-weight: bold;
            color: #f39c12;
            text-shadow: 0 0 10px rgba(243, 156, 18, 0.3);
            text-align: center;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .bottom-image {
            margin-top: 20px;
            max-width: 100%;
            height: auto;
            border-radius: 10px;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
            opacity: 0.9;
            transition: opacity 0.3s ease;
        }

        .bottom-image:hover {
            opacity: 1;
        }

        @media (max-width: 768px) {
            .container {
                width: 100%;
                height: auto;
                flex-direction: column;
            }

            .left-region, .right-region {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 左侧区域 -->
        <div class="left-region">
            <!-- 运行状态指示器 -->
            <div class="component">
                <div class="indicator">
                    <div class="status-circle" id="statusIndicator"></div>
                    <div>
                        <div class="label">运行状态</div>
                        <div class="value" id="statusText">停止</div>
                    </div>
                </div>
            </div>

                         <!-- 风扇转向 -->
             <div class="component">
                 <div class="label">风扇转向</div>
                 <div class="fan-direction">
                     <div class="direction-option" data-direction="正转">正转</div>
                     <div class="direction-option" data-direction="反转">反转</div>
                 </div>
             </div>

             <!-- 风扇转速 -->
             <div class="component">
                 <div class="label">风扇转速</div>
                 <div class="rpm-display" id="rpmValue">&nbsp;</div>
             </div>
        </div>

        <!-- 右侧区域 -->
        <div class="right-region">
            <!-- 标题 -->
            <div class="title">显示器</div>

                         <!-- 设定运行时间 -->
             <div class="parameter">
                 <div class="parameter-label">设定运行时间 t</div>
                 <div class="parameter-value">
                     <span id="setTime">&nbsp;</span>
                 </div>
             </div>

             <!-- 倒计时 -->
             <div class="parameter">
                 <div class="parameter-label">倒计时</div>
                 <div class="parameter-value">
                     <span id="countdown">&nbsp;</span>
                 </div>
             </div>

             <!-- 设定工作电压 -->
             <div class="parameter">
                 <div class="parameter-label">设定工作电压 Uₙ</div>
                 <div class="parameter-value">
                     <span id="setVoltage">&nbsp;</span>
                 </div>
             </div>

             <!-- 操作距离 -->
             <div class="parameter">
                 <div class="parameter-label">操作距离</div>
                 <div class="parameter-value">
                     <span id="distance">&nbsp;</span>
                 </div>
             </div>
        </div>
    </div>

    <!-- 底部图片 -->
    <img src="tuli.png" alt="底部图片" class="bottom-image">

    <script>
        // 状态切换功能
        let isRunning = false;
        const statusIndicator = document.getElementById('statusIndicator');
        const statusText = document.getElementById('statusText');

        function toggleStatus() {
            isRunning = !isRunning;
            if (isRunning) {
                statusIndicator.classList.add('running');
                statusText.textContent = '启动';
            } else {
                statusIndicator.classList.remove('running');
                statusText.textContent = '停止';
            }
        }

        // 风扇转向切换
        const directionOptions = document.querySelectorAll('.direction-option');
        directionOptions.forEach(option => {
            option.addEventListener('click', function() {
                directionOptions.forEach(opt => opt.classList.remove('active'));
                this.classList.add('active');
            });
        });

        // 模拟数据更新
        function updateValues() {
            // 只有在运行状态下才更新数据
            if (isRunning) {
                // 模拟RPM值
                const rpm = Math.floor(Math.random() * 3000) + 1000;
                document.getElementById('rpmValue').textContent = `${rpm} RPM`;

                // 模拟其他参数
                document.getElementById('setTime').textContent = Math.floor(Math.random() * 3600);
                document.getElementById('countdown').textContent = Math.floor(Math.random() * 300);
                document.getElementById('setVoltage').textContent = (Math.random() * 24 + 12).toFixed(1);
                document.getElementById('distance').textContent = Math.floor(Math.random() * 100 + 10);
            } else {
                // 停止状态下显示空格占位
                document.getElementById('rpmValue').innerHTML = '&nbsp;';
                document.getElementById('setTime').innerHTML = '&nbsp;';
                document.getElementById('countdown').innerHTML = '&nbsp;';
                document.getElementById('setVoltage').innerHTML = '&nbsp;';
                document.getElementById('distance').innerHTML = '&nbsp;';
            }
        }

        // 点击状态指示器切换状态
        statusIndicator.addEventListener('click', toggleStatus);

        // 定期更新数据
        setInterval(updateValues, 2000);
    </script>
</body>
</html> 